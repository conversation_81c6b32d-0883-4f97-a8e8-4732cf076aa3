import express from 'express';
import { PetStorage } from './petStorage';
import { JsonRpc<PERSON>rovider, Contract } from 'ethers';
import { z } from 'zod';
import { Request, Response, NextFunction } from 'express';

// Create router
const router = express.Router();
const petStorage = new PetStorage();

// NFT Contract ABI (simplified for this example)
const NFT_CONTRACT_ABI = [
  "function ownerOf(uint256 tokenId) public view returns (address)",
  "function tokenURI(uint256 tokenId) public view returns (string memory)"
];

// Environment detection
const isDevelopment = process.env.NODE_ENV !== 'production';

// Contract addresses - use different addresses based on network
const CONTRACT_ADDRESSES = {
  '137': '******************************************', // Polygon Mainnet
  '80001': '******************************************', // Mumbai Testnet
  '80002': '******************************************', // Amoy Testnet
};

// RPC URLs for different networks
const RPC_URLS = {
  '137': 'https://polygon-rpc.com/', // Polygon Mainnet
  '80001': 'https://rpc-mumbai.maticvigil.com/', // Mumbai Testnet
  '80002': 'https://rpc-amoy.polygon.technology/', // Amoy Testnet
};

// Default to Amoy testnet in development mode
const DEFAULT_CHAIN_ID = isDevelopment ? '80002' : '137';

// Create provider for blockchain queries
const provider = new JsonRpcProvider(RPC_URLS[DEFAULT_CHAIN_ID]);
const nftContract = new Contract(CONTRACT_ADDRESSES[DEFAULT_CHAIN_ID], NFT_CONTRACT_ABI, provider) as any;

// Validation schemas
const createPetSpecterSchema = z.object({
  gameId: z.string(),
  name: z.string(),
  ownerId: z.number().optional(),
  walletAddress: z.string().optional(),
  tokenId: z.string().optional(),
  specterType: z.string(),
  level: z.number().default(1),
  xp: z.number().default(0),
  traits: z.array(z.object({
    type: z.string(),
    level: z.number(),
    xp: z.number(),
    xpToNextLevel: z.number()
  })).optional(),
  equipment: z.object({
    weapon: z.object({
      id: z.string(),
      name: z.string(),
      attackBonus: z.number(),
      effects: z.array(z.string())
    }).optional(),
    armor: z.object({
      id: z.string(),
      name: z.string(),
      defenseBonus: z.number(),
      effects: z.array(z.string())
    }).optional(),
    utility: z.object({
      id: z.string(),
      name: z.string(),
      effects: z.array(z.string())
    }).optional()
  }).optional(),
  stats: z.object({
    health: z.number(),
    maxHealth: z.number(),
    attackPower: z.number(),
    defenseValue: z.number(),
    speed: z.number()
  }).optional(),
  metadata: z.record(z.any()).optional()
});

const updatePetSpecterSchema = createPetSpecterSchema.partial();

const createNftTransactionSchema = z.object({
  txHash: z.string(),
  tokenId: z.string(),
  walletAddress: z.string(),
  petSpecterId: z.number().optional().nullable(),
  price: z.string(),
  platformFee: z.string(),
  status: z.string().default("completed"),
  metadata: z.record(z.any()).optional()
});

// Middleware to verify NFT ownership
async function verifyNFTOwnership(req: Request, res: Response, next: NextFunction) {
  const tokenId = req.params.tokenId as string;
  const walletAddress = req.query.walletAddress as string;

  if (!tokenId || !walletAddress) {
    return res.status(400).json({ error: "Token ID and wallet address are required" });
  }

  // In development mode, skip blockchain verification
  if (isDevelopment) {
    console.log('Development mode: Skipping blockchain verification for NFT ownership');

    try {
      // Check if the token exists in our database and is owned by this wallet
      const petSpecter = await petStorage.getPetSpecterByTokenId(tokenId);

      if (!petSpecter) {
        return res.status(404).json({ error: "Pet specter not found" });
      }

      if (petSpecter.walletAddress && petSpecter.walletAddress.toLowerCase() !== walletAddress.toLowerCase()) {
        return res.status(403).json({ error: "You don't own this NFT" });
      }

      // Ownership verified in database, proceed
      next();
    } catch (error) {
      console.error("Error verifying NFT ownership in database:", error);
      return res.status(500).json({ error: "Failed to verify NFT ownership" });
    }

    return;
  }

  // Production mode: Verify on blockchain
  try {
    // Query the blockchain to verify ownership
    const owner = await nftContract.ownerOf(tokenId);

    // Check if the provided wallet address matches the owner
    if (owner.toLowerCase() !== walletAddress.toLowerCase()) {
      return res.status(403).json({ error: "You don't own this NFT" });
    }

    // Ownership verified, proceed
    next();
  } catch (error) {
    console.error("Error verifying NFT ownership:", error);
    return res.status(500).json({ error: "Failed to verify NFT ownership" });
  }
}

// Get all pet specters for a wallet address
router.get('/wallet/:walletAddress', async (req: Request, res: Response) => {
  try {
    const { walletAddress } = req.params;

    // Check if this is an OrangeID (typically starts with 'cl' or has an 'auth0|' prefix)
    const isOrangeID = walletAddress.startsWith('cl') || walletAddress.startsWith('auth0|');

    let petSpecters = [];

    if (isOrangeID) {
      // This is an OrangeID, so we need to check the metadata for OrangeID info
      console.log(`Fetching pet specters for OrangeID: ${walletAddress}`);

      // First, try to get pet specters directly associated with this ID as wallet address
      const directPets = await petStorage.getPetSpectersByWalletAddress(walletAddress);
      petSpecters = [...directPets];
      console.log(`Found ${directPets.length} pet specters directly associated with OrangeID: ${walletAddress}`);

      // Then, look for pet specters that have this OrangeID in their metadata
      const metadataPets = await petStorage.getPetSpectersByMetadataOrangeID(walletAddress);
      console.log(`Found ${metadataPets.length} pet specters with OrangeID in metadata: ${walletAddress}`);

      // Merge the results, avoiding duplicates
      const existingIds = new Set(petSpecters.map(p => p.id));
      for (const pet of metadataPets) {
        if (!existingIds.has(pet.id)) {
          petSpecters.push(pet);
          existingIds.add(pet.id);
        }
      }
    } else {
      // Regular wallet address
      console.log(`Fetching pet specters for wallet address: ${walletAddress}`);

      // Get pet specters directly associated with this wallet address
      petSpecters = await petStorage.getPetSpectersByWalletAddress(walletAddress);
      console.log(`Found ${petSpecters.length} pet specters directly associated with wallet: ${walletAddress}`);

      // Also check for any pet specters that might have been created with an OrangeID
      // but are associated with this wallet address in metadata
      const metadataPets = await petStorage.getPetSpectersByMetadataWalletAddress(walletAddress);
      console.log(`Found ${metadataPets.length} pet specters with wallet address in metadata: ${walletAddress}`);

      // Merge the results, avoiding duplicates
      const existingIds = new Set(petSpecters.map(p => p.id));
      for (const pet of metadataPets) {
        if (!existingIds.has(pet.id)) {
          petSpecters.push(pet);
          existingIds.add(pet.id);
        }
      }
    }

    console.log(`Returning a total of ${petSpecters.length} pet specters for user: ${walletAddress}`);
    res.json(petSpecters);
  } catch (error) {
    console.error("Error fetching pet specters:", error);
    res.status(500).json({ error: "Failed to fetch pet specters" });
  }
});

// Get pet specter by token ID (requires ownership verification)
router.get('/nft/:tokenId', verifyNFTOwnership, async (req, res) => {
  try {
    const { tokenId } = req.params;
    const petSpecter = await petStorage.getPetSpecterByTokenId(tokenId);

    if (!petSpecter) {
      return res.status(404).json({ error: "Pet specter not found" });
    }

    res.json(petSpecter);
  } catch (error) {
    console.error("Error fetching pet specter:", error);
    res.status(500).json({ error: "Failed to fetch pet specter" });
  }
});

// Get pet specter by game ID
router.get('/game-id/:gameId', async (req, res) => {
  try {
    const { gameId } = req.params;
    const petSpecter = await petStorage.getPetSpecterByGameId(gameId);

    if (!petSpecter) {
      return res.status(404).json({ error: "Pet specter not found" });
    }

    res.json(petSpecter);
  } catch (error) {
    console.error("Error fetching pet specter by game ID:", error);
    res.status(500).json({ error: "Failed to fetch pet specter by game ID" });
  }
});

// Create a new pet specter
router.post('/', async (req, res) => {
  try {
    const validatedData = createPetSpecterSchema.parse(req.body);

    // Check if this is an Orange Pet Specter and if user already has one
    const isOrangePet = validatedData.specterType.toLowerCase() === 'orange' ||
                       validatedData.specterType.toLowerCase() === 'orangepet';

    if (isOrangePet && validatedData.walletAddress) {
      console.log(`Checking for existing Orange Pet Specter for user: ${validatedData.walletAddress}`);

      // Check if user already has an Orange Pet Specter
      const existingPets = await petStorage.getPetSpectersByWalletAddress(validatedData.walletAddress);
      const hasOrangePet = existingPets.some(pet =>
        pet.specterType.toLowerCase() === 'orange' ||
        pet.specterType.toLowerCase() === 'orangepet'
      );

      if (hasOrangePet) {
        console.log(`User ${validatedData.walletAddress} already has an Orange Pet Specter, preventing duplicate creation`);
        return res.status(409).json({
          error: "User already owns an Orange Pet Specter. Only one Orange Pet Specter per user is allowed."
        });
      }

      // Also check by metadata for OrangeID users
      if (validatedData.walletAddress.startsWith('cl') || validatedData.walletAddress.startsWith('auth0|')) {
        const metadataPets = await petStorage.getPetSpectersByMetadataOrangeID(validatedData.walletAddress);
        const hasOrangePetInMetadata = metadataPets.some(pet =>
          pet.specterType.toLowerCase() === 'orange' ||
          pet.specterType.toLowerCase() === 'orangepet'
        );

        if (hasOrangePetInMetadata) {
          console.log(`OrangeID user ${validatedData.walletAddress} already has an Orange Pet Specter in metadata, preventing duplicate creation`);
          return res.status(409).json({
            error: "User already owns an Orange Pet Specter. Only one Orange Pet Specter per user is allowed."
          });
        }
      }
    }

    const petSpecter = await petStorage.createPetSpecter(validatedData);
    res.status(201).json(petSpecter);
  } catch (error) {
    console.error("Error creating pet specter:", error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: "Failed to create pet specter" });
  }
});

// Update a pet specter
router.put('/:id', async (req, res) => {
  try {
    const { id } = req.params;
    const validatedData = updatePetSpecterSchema.parse(req.body);

    const petSpecter = await petStorage.updatePetSpecter(parseInt(id), validatedData);

    if (!petSpecter) {
      return res.status(404).json({ error: "Pet specter not found" });
    }

    res.json(petSpecter);
  } catch (error) {
    console.error("Error updating pet specter:", error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: "Failed to update pet specter" });
  }
});

// Update pet XP progress
router.post('/update-xp', async (req, res) => {
  try {
    const { petId, ownerID, xpData } = req.body;

    if (!petId || !ownerID || !xpData) {
      return res.status(400).json({ error: "Pet ID, owner ID, and XP data are required" });
    }

    // Find the pet specter by game ID and owner
    const petSpecter = await petStorage.getPetSpecterByGameId(petId);

    if (!petSpecter) {
      return res.status(404).json({ error: "Pet specter not found" });
    }

    // Verify ownership (check both wallet address and metadata for OrangeID)
    const isOwner = petSpecter.walletAddress === ownerID ||
                   (petSpecter.metadata && petSpecter.metadata.orangeId === ownerID);

    if (!isOwner) {
      return res.status(403).json({ error: "You don't own this pet specter" });
    }

    // Update the pet's metadata with new XP data
    const updatedMetadata = {
      ...petSpecter.metadata,
      level: xpData.level,
      xp: xpData.xp,
      xpToNextLevel: xpData.xpToNextLevel,
      traits: xpData.traits,
      lastXpUpdate: new Date().toISOString()
    };

    // Update the pet specter in the database
    const updatedPetSpecter = await petStorage.updatePetSpecter(petSpecter.id, {
      level: xpData.level,
      xp: xpData.xp,
      metadata: updatedMetadata
    });

    if (!updatedPetSpecter) {
      return res.status(500).json({ error: "Failed to update pet specter XP" });
    }

    res.json({ success: true, petSpecter: updatedPetSpecter });
  } catch (error) {
    console.error("Error updating pet XP:", error);
    res.status(500).json({ error: "Failed to update pet XP" });
  }
});

// Link a pet specter to an NFT
router.post('/link-nft', async (req, res) => {
  try {
    const { gameId, tokenId, walletAddress } = req.body;

    if (!gameId || !tokenId || !walletAddress) {
      return res.status(400).json({ error: "Game ID, token ID, and wallet address are required" });
    }

    // Verify NFT ownership
    if (!isDevelopment) {
      try {
        const owner = await nftContract.ownerOf(tokenId);
        if (owner.toLowerCase() !== walletAddress.toLowerCase()) {
          return res.status(403).json({ error: "Wallet does not own this NFT" });
        }
      } catch (error) {
        console.error("Error verifying NFT ownership:", error);
        return res.status(400).json({ error: "Invalid NFT token ID" });
      }
    } else {
      console.log('Development mode: Skipping blockchain verification for NFT linking');
    }

    // Link the pet specter to the NFT
    const petSpecter = await petStorage.linkPetSpecterToNFT(gameId, tokenId, walletAddress);

    if (!petSpecter) {
      return res.status(404).json({ error: "Pet specter not found" });
    }

    res.json(petSpecter);
  } catch (error) {
    console.error("Error linking pet specter to NFT:", error);
    res.status(500).json({ error: "Failed to link pet specter to NFT" });
  }
});

// Record an NFT transaction
router.post('/transactions', async (req, res) => {
  try {
    const validatedData = createNftTransactionSchema.parse(req.body);
    const transaction = await petStorage.createNftTransaction(validatedData);
    res.status(201).json(transaction);
  } catch (error) {
    console.error("Error recording NFT transaction:", error);
    if (error instanceof z.ZodError) {
      return res.status(400).json({ error: error.errors });
    }
    res.status(500).json({ error: "Failed to record NFT transaction" });
  }
});

// Get NFT transactions for a wallet
router.get('/transactions/wallet/:walletAddress', async (req, res) => {
  try {
    const { walletAddress } = req.params;
    const transactions = await petStorage.getNftTransactionsByWalletAddress(walletAddress);
    res.json(transactions);
  } catch (error) {
    console.error("Error fetching NFT transactions:", error);
    res.status(500).json({ error: "Failed to fetch NFT transactions" });
  }
});

// Deploy a pet specter
router.post('/deploy/:gameId', async (req, res) => {
  try {
    const { gameId } = req.params;
    const { userIdentifier } = req.body;

    if (!userIdentifier) {
      return res.status(400).json({ error: "User identifier is required" });
    }

    // Get the pet specter
    const petSpecter = await petStorage.getPetSpecterByGameId(gameId);
    if (!petSpecter) {
      return res.status(404).json({ error: "Pet specter not found" });
    }

    // Verify ownership
    const metadata = petSpecter.metadata as any;
    const isOwner = petSpecter.walletAddress === userIdentifier ||
                   (metadata && metadata.orangeId === userIdentifier);

    if (!isOwner) {
      return res.status(403).json({ error: "You don't own this pet specter" });
    }

    // Update the pet's metadata to mark it as deployed
    const updatedMetadata = {
      ...(metadata || {}),
      isActive: true,
      deployedAt: new Date().toISOString()
    };

    const updatedPetSpecter = await petStorage.updatePetSpecter(petSpecter.id, {
      metadata: updatedMetadata
    });

    if (!updatedPetSpecter) {
      return res.status(500).json({ error: "Failed to deploy pet specter" });
    }

    res.json({ success: true, petSpecter: updatedPetSpecter });
  } catch (error) {
    console.error("Error deploying pet specter:", error);
    res.status(500).json({ error: "Failed to deploy pet specter" });
  }
});

// Recall a pet specter
router.post('/recall/:gameId', async (req, res) => {
  try {
    const { gameId } = req.params;
    const { userIdentifier } = req.body;

    if (!userIdentifier) {
      return res.status(400).json({ error: "User identifier is required" });
    }

    // Get the pet specter
    const petSpecter = await petStorage.getPetSpecterByGameId(gameId);
    if (!petSpecter) {
      return res.status(404).json({ error: "Pet specter not found" });
    }

    // Verify ownership
    const metadata = petSpecter.metadata as any;
    const isOwner = petSpecter.walletAddress === userIdentifier ||
                   (metadata && metadata.orangeId === userIdentifier);

    if (!isOwner) {
      return res.status(403).json({ error: "You don't own this pet specter" });
    }

    // Update the pet's metadata to mark it as recalled
    const updatedMetadata = {
      ...(metadata || {}),
      isActive: false,
      recalledAt: new Date().toISOString()
    };

    const updatedPetSpecter = await petStorage.updatePetSpecter(petSpecter.id, {
      metadata: updatedMetadata
    });

    if (!updatedPetSpecter) {
      return res.status(500).json({ error: "Failed to recall pet specter" });
    }

    res.json({ success: true, petSpecter: updatedPetSpecter });
  } catch (error) {
    console.error("Error recalling pet specter:", error);
    res.status(500).json({ error: "Failed to recall pet specter" });
  }
});

export default router;
